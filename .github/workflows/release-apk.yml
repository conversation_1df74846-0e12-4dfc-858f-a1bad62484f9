name: Create Release from Committed APK

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version_bump:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  create-release:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Get version from tag or calculate next version
        id: version_info
        run: |
          if [ "${{ github.event_name }}" = "push" ] && [[ "${{ github.ref }}" == refs/tags/* ]]; then
            # Extract version from tag
            TAG_NAME=${GITHUB_REF#refs/tags/}
            VERSION_NUMBER=${TAG_NAME#v}
            echo "new_version=$TAG_NAME" >> $GITHUB_OUTPUT
            echo "version_number=$VERSION_NUMBER" >> $GITHUB_OUTPUT
            echo "Using tag version: $TAG_NAME"
          else
            # Calculate next version for manual dispatch
            LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
            VERSION_BUMP="${{ github.event.inputs.version_bump || 'patch' }}"

            # Remove 'v' prefix if present
            CURRENT_VERSION=${LATEST_TAG#v}

            # Split version into parts
            IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
            MAJOR=${VERSION_PARTS[0]:-0}
            MINOR=${VERSION_PARTS[1]:-0}
            PATCH=${VERSION_PARTS[2]:-0}

            # Increment based on bump type
            case $VERSION_BUMP in
              major)
                MAJOR=$((MAJOR + 1))
                MINOR=0
                PATCH=0
                ;;
              minor)
                MINOR=$((MINOR + 1))
                PATCH=0
                ;;
              patch)
                PATCH=$((PATCH + 1))
                ;;
            esac

            NEW_VERSION="v${MAJOR}.${MINOR}.${PATCH}"
            echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
            echo "version_number=${MAJOR}.${MINOR}.${PATCH}" >> $GITHUB_OUTPUT
            echo "Calculated next version: $NEW_VERSION"
          fi

      - name: Check for committed APK
        id: check_apk
        run: |
          # Check if APK exists in releases directory
          if [ -f "releases/DCCPHub-latest.apk" ]; then
            APK_PATH="releases/DCCPHub-latest.apk"
            NEW_APK_NAME="DCCPHub-${{ steps.version_info.outputs.version_number }}.apk"

            # Copy and rename APK
            cp "$APK_PATH" "$NEW_APK_NAME"

            echo "apk_path=$NEW_APK_NAME" >> $GITHUB_OUTPUT
            echo "apk_name=$NEW_APK_NAME" >> $GITHUB_OUTPUT

            # Get APK size
            APK_SIZE=$(du -h "$NEW_APK_NAME" | cut -f1)
            echo "apk_size=$APK_SIZE" >> $GITHUB_OUTPUT

            echo "Found committed APK: $APK_PATH"
            echo "Renamed to: $NEW_APK_NAME"
            echo "APK size: $APK_SIZE"
            echo "Note: APK includes custom DCCP branding and adaptive icons"
          else
            echo "No committed APK found in releases/ directory"
            echo "Please build and commit an APK first using: ./scripts/build-apk-capacitor.sh"
            exit 1
          fi

      - name: Generate release notes
        id: release_notes
        run: |
          cat > release_notes.md << EOF
          # DCCPHub Mobile App ${{ steps.version_info.outputs.new_version }}

          ## 📱 Android APK Download

          Download the latest DCCPHub mobile app for Android devices.

          ### 📋 Installation Instructions

          1. **Download** the APK file below
          2. **Enable** "Install from unknown sources" in your Android settings:
             - Go to Settings > Security > Unknown Sources (Android 7 and below)
             - Go to Settings > Apps > Special Access > Install Unknown Apps (Android 8+)
          3. **Install** the downloaded APK file
          4. **Open** the DCCPHub app and sign in

          ### ✨ Features

          - 🔐 **Secure Authentication** with Google OAuth
          - 📊 **Dashboard** with real-time data
          - 📱 **Mobile-optimized** interface
          - 🔄 **Offline support** with PWA capabilities
          - 🎨 **Custom branding** with DCCP logo

          ### 📊 Build Information

          - **Version**: ${{ steps.version_info.outputs.version_number }}
          - **Build Type**: Debug
          - **Platform**: Android
          - **Size**: ${{ steps.check_apk.outputs.apk_size }}
          - **Built**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")

          ### 🔧 Technical Details

          - Built with Capacitor and Android Gradle
          - Targets Android API 34
          - Minimum Android version: 7.0 (API 24)
          - Uses custom DCCP branding and icons

          ---

          **Note**: This is a debug build for testing purposes. For production use, please contact the development team.
          EOF

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.version_info.outputs.new_version }}
          release_name: DCCPHub Mobile App ${{ steps.version_info.outputs.new_version }}
          body_path: release_notes.md
          draft: false
          prerelease: false

      - name: Upload APK to Release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ${{ steps.check_apk.outputs.apk_path }}
          asset_name: ${{ steps.check_apk.outputs.apk_name }}
          asset_content_type: application/vnd.android.package-archive

      - name: Output release information
        run: |
          echo "🎉 Release created successfully!"
          echo "📱 APK: ${{ steps.check_apk.outputs.apk_name }}"
          echo "📦 Size: ${{ steps.check_apk.outputs.apk_size }}"
          echo "🔗 Release URL: ${{ steps.create_release.outputs.html_url }}"
          echo "📥 Download URL: ${{ steps.create_release.outputs.html_url }}/download/${{ steps.check_apk.outputs.apk_name }}"
