<template>
    <div>
        <Sonner position="top-center" rich-colors close-button expand />

        <!-- Mobile Top Navigation Bar -->

        <SidebarProvider>
            <Sidebar variant="inset">
                <SidebarHeader>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <SidebarMenuButton size="lg" as-child>
                                <Link :href="route('faculty.dashboard')">
                                    <div
                                        class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground"
                                    >
                                        <Command class="size-4" />
                                    </div>
                                    <div
                                        class="grid flex-1 text-left text-sm leading-tight"
                                    >
                                        <span class="truncate font-medium"
                                            >Faculty Portal</span
                                        >
                                        <span class="truncate text-xs"
                                            >Teaching Hub</span
                                        >
                                    </div>
                                </Link>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarHeader>

                <SidebarContent>
                    <NavMain :items="sidebarData.navMain" />
                    <NavSecondary
                        :items="sidebarData.navSecondary"
                        class="mt-auto"
                    />
                </SidebarContent>

                <SidebarFooter>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <FacultyUserProfile variant="sidebar" />
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarFooter>
            </Sidebar>

            <SidebarInset>
                <header
                    class="flex h-16 shrink-0 items-center gap-2 hidden md:flex"
                >
                    <div class="flex items-center gap-2 px-4">
                        <SidebarTrigger class="-ml-1" />
                        <Separator
                            orientation="vertical"
                            class="mr-2 data-[orientation=vertical]:h-4"
                        />
                        <Breadcrumb>
                            <BreadcrumbList>
                                <BreadcrumbItem class="hidden md:block">
                                    <BreadcrumbLink
                                        :href="route('faculty.dashboard')"
                                    >
                                        Faculty Portal
                                    </BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbSeparator class="hidden md:block" />
                                <BreadcrumbItem>
                                    <BreadcrumbPage>
                                        <slot name="header">Dashboard</slot>
                                    </BreadcrumbPage>
                                </BreadcrumbItem>
                            </BreadcrumbList>
                        </Breadcrumb>
                    </div>
                    <div class="ml-auto flex items-center gap-2 px-4">
                        <!-- Notifications for authenticated users -->
                        <NotificationDropdown
                            ref="notificationDropdown"
                            class="hidden md:block"
                        />
                        <SemesterSchoolYearSelector />

                        <!-- Test Notification Button (Development) -->
                        <Button
                            v-if="$page.props.app?.env === 'local'"
                            variant="ghost"
                            size="icon"
                            @click="sendTestNotificationHandler"
                            title="Send Test Notification"
                        >
                            <Icon icon="lucide:beaker" class="h-5 w-5" />
                        </Button>
                    </div>
                </header>

                <main class="flex flex-1 flex-col gap-4 p-4 pt-0 mt-14 md:mt-0">
                    <slot />
                </main>

                <!-- Compact Footer -->
                <footer class="py-3 px-4 border-t mb-14 md:mb-0">
                    <div
                        class="container mx-auto flex flex-wrap items-center justify-between gap-y-4"
                    >
                        <!-- Copyright -->
                        <div class="text-center md:text-left">
                            <p class="text-sm text-muted-foreground">
                                &copy; {{ new Date().getFullYear() }} Faculty
                                Portal. All rights reserved.
                            </p>
                        </div>

                        <!-- Social Links -->
                        <div class="flex items-center gap-3">
                            <Link
                                href="https://github.com/yukazakiri"
                                class="text-muted-foreground hover:text-primary transition-colors"
                                target="_blank"
                            >
                                <Icon icon="lucide:github" class="h-5 w-5" />
                            </Link>
                            <Link
                                href="#"
                                class="text-muted-foreground hover:text-primary transition-colors"
                                target="_blank"
                            >
                                <Icon icon="lucide:twitter" class="h-5 w-5" />
                            </Link>
                        </div>

                        <!-- Tech Stack -->
                        <div
                            class="flex items-center gap-2 text-muted-foreground text-sm"
                        >
                            <Icon icon="logos:laravel" class="h-4 w-4" />
                            <Icon icon="logos:vue" class="h-4 w-4" />
                            <Icon icon="logos:inertiajs-icon" class="h-4 w-4" />
                            <Icon
                                icon="logos:tailwindcss-icon"
                                class="h-4 w-4"
                            />
                            <Icon icon="logos:vitejs" class="h-4 w-4" />
                        </div>
                    </div>
                </footer>
            </SidebarInset>
        </SidebarProvider>

        <!-- Redesigned Mobile Bottom Navigation -->
        <div
            class="fixed bottom-0 left-0 right-0 border-t bg-background/80 backdrop-blur-sm z-50 md:hidden"
        >
            <div class="grid grid-cols-5 px-1 py-1">
                <Link
                    v-for="item in mobileNavConfig.quickActions.slice(0, 4)"
                    :key="item.name"
                    :href="route ? route(item.route) : `/${item.route}`"
                    class="flex flex-col items-center py-2 px-1 rounded-lg transition-colors"
                    :class="
                        route && route().current(item.route)
                            ? 'text-primary bg-secondary/50'
                            : 'text-muted-foreground hover:bg-secondary/30'
                    "
                    prefetch
                >
                    <Icon :icon="item.icon" class="h-5 w-5 mb-1" />
                    <span class="text-xs">{{ item.name }}</span>
                </Link>
                <button
                    class="flex flex-col items-center py-2 px-1 text-muted-foreground rounded-lg hover:bg-secondary/30 transition-colors"
                    @click="isMobileMenuOpen = true"
                >
                    <Icon icon="lucide:menu" class="h-5 w-5 mb-1" />
                    <span class="text-xs">Menu</span>
                </button>
            </div>
        </div>

        <!-- Mobile Menu Sheet -->
        <Sheet
            :open="isMobileMenuOpen"
            @update:open="isMobileMenuOpen = $event"
        >
            <SheetContent side="left" class="w-[300px] p-0">
                <SheetHeader class="p-6 pb-4">
                    <SheetTitle class="text-left">Faculty Portal</SheetTitle>
                </SheetHeader>

                <!-- Sheet Content - Same as Sidebar -->
                <div class="flex flex-col h-full">
                    <!-- Header Section -->
                    <div class="px-6 pb-4">
                        <div class="flex items-center gap-3">
                            <div
                                class="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground"
                            >
                                <Command class="size-4" />
                            </div>
                            <div
                                class="grid flex-1 text-left text-sm leading-tight"
                            >
                                <span class="truncate font-medium"
                                    >Faculty Portal</span
                                >
                                <span
                                    class="truncate text-xs text-muted-foreground"
                                    >Teaching Hub</span
                                >
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Content -->
                    <div class="flex-1 px-6 space-y-4">
                        <!-- Main Navigation -->
                        <div class="space-y-2">
                            <h4
                                class="text-sm font-medium text-muted-foreground mb-2"
                            >
                                Platform
                            </h4>
                            <nav class="space-y-1">
                                <template
                                    v-for="item in sidebarData.navMain"
                                    :key="item.title"
                                >
                                    <!-- Single item without subitems -->
                                    <Link
                                        v-if="!item.items?.length"
                                        :href="item.disabled ? '#' : item.url"
                                        class="flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                                        :class="
                                            item.isActive
                                                ? 'bg-accent text-accent-foreground'
                                                : 'text-muted-foreground'
                                        "
                                        @click="
                                            item.disabled &&
                                                $event.preventDefault();
                                            !item.disabled &&
                                                (isMobileMenuOpen = false);
                                        "
                                    >
                                        <component
                                            :is="item.icon"
                                            class="h-4 w-4"
                                        />
                                        <span>{{ item.title }}</span>
                                    </Link>

                                    <!-- Item with subitems -->
                                    <div v-else class="space-y-1">
                                        <div
                                            class="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-muted-foreground"
                                        >
                                            <component
                                                :is="item.icon"
                                                class="h-4 w-4"
                                            />
                                            <span class="font-medium">{{
                                                item.title
                                            }}</span>
                                        </div>
                                        <div class="ml-7 space-y-1">
                                            <Link
                                                v-for="subItem in item.items"
                                                :key="subItem.title"
                                                :href="
                                                    subItem.disabled
                                                        ? '#'
                                                        : subItem.url
                                                "
                                                class="block rounded-lg px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                                                :class="
                                                    subItem.disabled
                                                        ? 'text-muted-foreground/50 cursor-not-allowed'
                                                        : 'text-muted-foreground'
                                                "
                                                @click="
                                                    subItem.disabled &&
                                                        $event.preventDefault();
                                                    !subItem.disabled &&
                                                        (isMobileMenuOpen = false);
                                                "
                                            >
                                                {{ subItem.title }}
                                            </Link>
                                        </div>
                                    </div>
                                </template>
                            </nav>
                        </div>

                        <!-- Secondary Navigation -->
                        <div class="space-y-2">
                            <nav class="space-y-1">
                                <Link
                                    v-for="item in sidebarData.navSecondary"
                                    :key="item.title"
                                    :href="item.disabled ? '#' : item.url"
                                    :target="
                                        item.url.startsWith('http')
                                            ? '_blank'
                                            : undefined
                                    "
                                    class="flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                                    :class="
                                        item.disabled
                                            ? 'text-muted-foreground/50 cursor-not-allowed'
                                            : 'text-muted-foreground'
                                    "
                                    @click="
                                        item.disabled &&
                                            $event.preventDefault();
                                        !item.disabled &&
                                            !item.url.startsWith('http') &&
                                            (isMobileMenuOpen = false);
                                    "
                                >
                                    <component
                                        :is="item.icon"
                                        class="h-4 w-4"
                                    />
                                    <span>{{ item.title }}</span>
                                </Link>
                            </nav>
                        </div>
                    </div>

                    <!-- Footer with User Profile -->
                    <div class="p-6 pt-4 border-t">
                        <FacultyUserProfile variant="mobile" />
                    </div>
                </div>
            </SheetContent>
        </Sheet>

        <!-- Development Modal -->
        <DevelopmentModal
            :open="showDevelopmentModal"
            @update:open="showDevelopmentModal = $event"
        />
    </div>
</template>

<script setup>
import FacultyUserProfile from "@/Components/Faculty/FacultyUserProfile.vue";
import NotificationDropdown from "@/Components/Notifications/NotificationDropdown.vue";
import SemesterSchoolYearSelector from "@/Components/SemesterSchoolYearSelector.vue";
import NavMain from "@/Components/shadcn/NavMain.vue";
import NavSecondary from "@/Components/shadcn/NavSecondary.vue";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/Components/shadcn/ui/breadcrumb";
import { Button } from "@/Components/shadcn/ui/button";
import { Separator } from "@/Components/shadcn/ui/separator";
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from "@/Components/shadcn/ui/sheet";
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarInset,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarProvider,
    SidebarTrigger,
} from "@/Components/shadcn/ui/sidebar";
import Sonner from "@/Components/shadcn/ui/sonner/Sonner.vue";
import DevelopmentModal from "@/Components/ui/DevelopmentModal.vue";
import { useNotifications } from "@/Composables/useNotifications";
import { useRealtimeNotifications } from "@/Composables/useRealtimeNotifications";
import { Icon } from "@iconify/vue";
import { Link, router, usePage } from "@inertiajs/vue3";
import { useColorMode } from "@vueuse/core";
import {
    BookOpen,
    Command,
    FileText,
    GraduationCap,
    LayoutDashboard,
    LifeBuoy,
    Settings,
} from "lucide-vue-next";
import { computed, inject, onMounted, ref, watch } from "vue";
import { toast } from "vue-sonner";

// Reactive state
const isMobileMenuOpen = ref(false);
const showDevelopmentModal = ref(false);
const notificationDropdown = ref(null);

// Page and user data
const page = usePage();
const user = computed(() => page.props.auth?.user);

// Notification composables
const { sendTestNotification } = useNotifications();

// Initialize real-time notifications
useRealtimeNotifications();

// Route helper function
const route = inject("route", null);

// Theme management
const mode = useColorMode({
    attribute: "class",
    modes: { light: "", dark: "dark" },
    initialValue: "light",
});
const isDarkMode = computed(() => mode.value === "dark");

// Enhanced sidebar data structure
const sidebarData = computed(() => ({
    navMain: [
        {
            title: "Dashboard",
            url: route ? route("faculty.dashboard") : "/faculty/dashboard",
            icon: LayoutDashboard,
            isActive: route ? route().current("faculty.dashboard") : false,
        },
        {
            title: "Teaching",
            url: "",
            icon: GraduationCap,
            items: [
                {
                    title: "My Classes",
                    url: route
                        ? route("faculty.classes.index")
                        : "/faculty/classes",
                },
                {
                    title: "Students",
                    url: route
                        ? route("faculty.students.index")
                        : "/faculty/students",
                },
                {
                    title: "Schedule",
                    url: route
                        ? route("faculty.schedule.index")
                        : "/faculty/schedule",
                },
            ],
        },
        {
            title: "Academic Tools",
            url: "",
            icon: BookOpen,
            items: [
                {
                    title: "Attendance",
                    url: route
                        ? route("faculty.attendance.index")
                        : "/faculty/attendance",
                },
                {
                    title: "Reports",
                    url: route
                        ? route("faculty.attendance.reports")
                        : "/faculty/attendance/reports",
                },
                {
                    title: "Analytics",
                    url: route
                        ? route("faculty.attendance.analytics")
                        : "/faculty/attendance/analytics",
                },
                {
                    title: "Grades",
                    url: "",
                    disabled: true,
                },
                {
                    title: "Assignments",
                    url: "",
                    disabled: true,
                },
                {
                    title: "Resources",
                    url: "",
                    disabled: true,
                },
            ],
        },
        {
            title: "Settings",
            url: route ? route("profile.show") : "/profile",
            icon: Settings,
            isActive: route ? route().current("profile.*") : false,
        },
    ],
    navSecondary: [
        {
            title: "Support",
            url: "https://github.com/yukazakiri/DccpHubv2/issues",
            icon: LifeBuoy,
        },
        {
            title: "Documentation",
            url: "",
            disabled: true,
            icon: FileText,
        },
    ],
}));

// Mobile navigation configuration
const mobileNavConfig = computed(() => ({
    quickActions: [
        {
            name: "Dashboard",
            icon: "lucide:layout-dashboard",
            route: "faculty.dashboard",
        },
        {
            name: "Classes",
            icon: "lucide:graduation-cap",
            route: "faculty.classes.index",
        },
        {
            name: "Attendance",
            icon: "lucide:clipboard-list",
            route: "faculty.attendance.index",
        },
        {
            name: "Schedule",
            icon: "lucide:calendar",
            route: "faculty.schedule.index",
        },
    ],
    mainMenu: [
        {
            name: "Attendance Reports",
            icon: "lucide:file-bar-chart",
            route: "faculty.attendance.reports",
        },
        {
            name: "Settings",
            icon: "lucide:settings",
            route: "profile.show",
        },
        {
            name: "Support",
            icon: "lucide:life-buoy",
            href: "https://github.com/yukazakiri/DccpHubv2/issues",
            external: true,
        },
        {
            name: "Documentation",
            icon: "lucide:file-text",
            route: "",
            disabled: true,
        },
    ],
}));

// Helper functions
function renderLink(item) {
    if (item.external) {
        return {
            is: "a",
            href: item.href || (route ? route(item.route) : `/${item.route}`),
            target: "_blank",
        };
    }
    return {
        is: Link,
        href: route ? route(item.route) : `/${item.route}`,
    };
}

function logout() {
    if (route) {
        router.post(route("logout"));
    } else {
        router.post("/logout");
    }
}

// Send test notification
const sendTestNotificationHandler = async () => {
    try {
        await sendTestNotification();
        // Refresh the notification dropdown
        if (notificationDropdown.value) {
            notificationDropdown.value.refresh();
        }
    } catch (error) {
        console.error("Failed to send test notification:", error);
        toast.error("Failed to send test notification");
    }
};

// Handle flash messages
const handleFlashMessages = () => {
    // Safely check if flash exists in page props
    if (!page.props.flash) return;

    try {
        if (page.props.flash.success) {
            toast.success(page.props.flash.success);
        }

        if (page.props.flash.error) {
            toast.error(page.props.flash.error);
        }

        if (page.props.flash.message) {
            toast.info(page.props.flash.message);
        }
    } catch (error) {
        console.error("Error displaying toast notification:", error);
    }
};

onMounted(() => {
    // Handle flash messages on initial load
    handleFlashMessages();
});

// Watch for flash messages when page changes
watch(
    () => page.props.flash,
    () => {
        handleFlashMessages();
    },
    { deep: true },
);
</script>
